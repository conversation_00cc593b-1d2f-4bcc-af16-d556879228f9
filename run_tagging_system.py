#!/usr/bin/env python3
"""
Main script to run the improved ML-based product tagging system.

This script provides a clean interface to run the tagging system with
all the critical improvements implemented.
"""

import sys
import os
sys.path.append('scripts')

from improved_ml_tagging_system import ImprovedProductTaggingSystem

def main():
    """Main function to run the tagging system"""
    print("🚀 Starting Improved ML-Based Product Tagging System")
    print("=" * 60)
    
    # Change to data directory for file access
    original_dir = os.getcwd()
    os.chdir('data')
    
    try:
        # Initialize and run the system
        system = ImprovedProductTaggingSystem()
        results = system.run_complete_pipeline()
        
        print(f"\n✅ Successfully processed {len(results)} products")
        print("📁 Output saved to: data/products_with_predicted_tags.csv")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    finally:
        # Return to original directory
        os.chdir(original_dir)
    
    print("\n🎯 Tagging system completed successfully!")
    return 0

if __name__ == "__main__":
    exit(main())
