#!/usr/bin/env python3

import pandas as pd
import ast
import random
from collections import Counter, defaultdict

class ManualConfidenceAnalyzer:
    def __init__(self):
        self.all_tags = []
        self.df_train = None
        self.df_target = None
        self.df_results = None
        
    def load_data(self):
        """Load all data for analysis"""
        print("Loading data for manual analysis...")
        
        # Load master tags
        with open('all_tags.txt', 'r') as f:
            self.all_tags = ast.literal_eval(f.read().strip())
        
        # Load datasets
        self.df_train = pd.read_csv('products_with_tags.csv', low_memory=False)
        self.df_target = pd.read_csv('products_without_tags.csv', low_memory=False)
        self.df_results = pd.read_csv('products_with_predicted_tags.csv')
        
        print(f"Loaded {len(self.all_tags)} tags, {len(self.df_train)} training products, {len(self.df_target)} target products")
    
    def analyze_sample_predictions(self, n_samples=10):
        """Manually analyze a sample of predictions"""
        print(f"\n=== MANUAL ANALYSIS OF {n_samples} SAMPLE PREDICTIONS ===")
        
        # Get random sample
        sample_indices = random.sample(range(len(self.df_results)), n_samples)
        
        for i, idx in enumerate(sample_indices):
            print(f"\n--- SAMPLE {i+1} ---")
            
            # Get prediction data
            result_row = self.df_results.iloc[idx]
            product_id = result_row['id']
            predicted_tags = result_row['tags'].split(',')
            
            # Find corresponding target product
            target_row = self.df_target[self.df_target['id'] == product_id].iloc[0]
            
            # Extract key information
            title = target_row.get('title', 'N/A')
            description = target_row.get('description', 'N/A')
            product_type = target_row.get('productType', 'N/A')
            
            # Get product details
            color = target_row.get('product_detail.Color', 'N/A')
            material = target_row.get('product_detail.Material ', 'N/A')
            stone = target_row.get('product_detail.Stone', 'N/A')
            jewelry_type = target_row.get('product_detail.Jewelry type ', 'N/A')
            
            print(f"Product ID: {product_id}")
            print(f"Title: {title}")
            print(f"Description: {description[:100]}..." if len(str(description)) > 100 else f"Description: {description}")
            print(f"Product Type: {product_type}")
            print(f"Color: {color}")
            print(f"Material: {material}")
            print(f"Stone: {stone}")
            print(f"Jewelry Type: {jewelry_type}")
            
            print(f"\nPredicted Tags ({len(predicted_tags)}):")
            for j, tag in enumerate(predicted_tags):
                print(f"  {j+1:2d}. {tag}")
            
            # Analyze tag relevance
            self._analyze_tag_relevance(title, description, product_type, predicted_tags, color, material, stone, jewelry_type)
    
    def _analyze_tag_relevance(self, title, description, product_type, predicted_tags, color, material, stone, jewelry_type):
        """Analyze the relevance of predicted tags"""
        print(f"\n  RELEVANCE ANALYSIS:")
        
        # Combine text for analysis
        combined_text = f"{title} {description} {product_type} {color} {material} {stone} {jewelry_type}".lower()
        
        relevant_tags = []
        questionable_tags = []
        good_tags = []
        
        for tag in predicted_tags:
            tag_lower = tag.lower()
            
            # Check direct mentions
            if tag_lower in combined_text:
                relevant_tags.append(f"✓ '{tag}' - directly mentioned")
                good_tags.append(tag)
            
            # Check semantic relevance
            elif self._check_semantic_relevance(tag, combined_text, product_type):
                relevant_tags.append(f"✓ '{tag}' - semantically relevant")
                good_tags.append(tag)
            
            # Check if it's a reasonable inference
            elif self._check_reasonable_inference(tag, combined_text, product_type):
                relevant_tags.append(f"~ '{tag}' - reasonable inference")
            
            else:
                questionable_tags.append(f"? '{tag}' - questionable relevance")
        
        print(f"    Good tags: {len(good_tags)}/{len(predicted_tags)} ({len(good_tags)/len(predicted_tags)*100:.1f}%)")
        
        if relevant_tags:
            print(f"    Relevant tags:")
            for tag_analysis in relevant_tags[:5]:  # Show first 5
                print(f"      {tag_analysis}")
        
        if questionable_tags:
            print(f"    Questionable tags:")
            for tag_analysis in questionable_tags[:3]:  # Show first 3
                print(f"      {tag_analysis}")
    
    def _check_semantic_relevance(self, tag, text, product_type):
        """Check if tag is semantically relevant"""
        tag_lower = tag.lower()
        
        # Jewelry type mappings
        jewelry_mappings = {
            'ring': ['ring', 'band'],
            'earrings': ['earring', 'ear'],
            'necklace': ['necklace', 'chain', 'pendant'],
            'bracelet': ['bracelet', 'bangle'],
            'pendant': ['pendant', 'charm', 'necklace']
        }
        
        # Material mappings
        material_mappings = {
            'gold': ['gold', 'yellow', 'golden'],
            'silver': ['silver', 'sterling'],
            'white gold': ['white', 'gold'],
            'rose gold': ['rose', 'pink', 'gold']
        }
        
        # Check jewelry type relevance
        for jewelry_type, keywords in jewelry_mappings.items():
            if jewelry_type in tag_lower:
                return any(keyword in text for keyword in keywords)
        
        # Check material relevance
        for material, keywords in material_mappings.items():
            if material in tag_lower:
                return any(keyword in text for keyword in keywords)
        
        # Check gender relevance
        if 'women' in tag_lower or 'men' in tag_lower:
            return True  # Generally applicable
        
        return False
    
    def _check_reasonable_inference(self, tag, text, product_type):
        """Check if tag is a reasonable inference"""
        tag_lower = tag.lower()
        
        # Common reasonable inferences
        if 'jewelry' in tag_lower and any(j in text for j in ['ring', 'earring', 'necklace', 'bracelet']):
            return True
        
        if '14kt' in tag_lower and 'gold' in text:
            return True
        
        if 'setting' in tag_lower and any(s in text for s in ['stone', 'diamond', 'gem']):
            return True
        
        return False
    
    def analyze_585_1000_usage(self):
        """Specifically analyze 585/1000 tag usage"""
        print(f"\n=== ANALYSIS OF 585/1000 TAG USAGE ===")
        
        # Find products with 585/1000 tag
        products_with_585 = []
        for _, row in self.df_results.iterrows():
            if '585/1000' in row['tags']:
                products_with_585.append(row['id'])
        
        print(f"Products with 585/1000 tag: {len(products_with_585)}")
        
        # Analyze a sample
        sample_585 = random.sample(products_with_585, min(5, len(products_with_585)))
        
        for product_id in sample_585:
            target_row = self.df_target[self.df_target['id'] == product_id].iloc[0]
            title = target_row.get('title', '')
            description = target_row.get('description', '')
            material = target_row.get('product_detail.Material ', '')
            
            combined_text = f"{title} {description} {material}".lower()
            
            print(f"\nProduct: {product_id}")
            print(f"Title: {title}")
            print(f"Material: {material}")
            
            # Check for explicit mentions
            gold_mentions = []
            if '585' in combined_text:
                gold_mentions.append("585 explicitly mentioned")
            if '14k' in combined_text or '14kt' in combined_text:
                gold_mentions.append("14K mentioned")
            if 'gold' in combined_text:
                gold_mentions.append("gold mentioned")
            
            if gold_mentions:
                print(f"  ✓ Justified: {', '.join(gold_mentions)}")
            else:
                print(f"  ? Questionable: No explicit gold/585/14K mentions found")
    
    def analyze_tag_distribution(self):
        """Analyze the distribution of predicted tags"""
        print(f"\n=== TAG DISTRIBUTION ANALYSIS ===")
        
        all_predicted_tags = []
        for _, row in self.df_results.iterrows():
            tags = row['tags'].split(',')
            all_predicted_tags.extend(tags)
        
        tag_counts = Counter(all_predicted_tags)
        
        print(f"Total tag instances: {len(all_predicted_tags)}")
        print(f"Unique tags used: {len(tag_counts)}")
        print(f"Coverage of master vocabulary: {len(tag_counts)}/{len(self.all_tags)} ({len(tag_counts)/len(self.all_tags)*100:.1f}%)")
        
        print(f"\nMost frequently predicted tags:")
        for tag, count in tag_counts.most_common(15):
            percentage = (count / len(self.df_results)) * 100
            print(f"  {tag}: {count} products ({percentage:.1f}%)")
        
        print(f"\nLeast frequently predicted tags:")
        for tag, count in list(tag_counts.most_common())[-10:]:
            percentage = (count / len(self.df_results)) * 100
            print(f"  {tag}: {count} products ({percentage:.1f}%)")
    
    def compare_with_training_patterns(self):
        """Compare predictions with training data patterns"""
        print(f"\n=== COMPARISON WITH TRAINING PATTERNS ===")
        
        # Analyze training tag patterns
        training_tags = []
        for _, row in self.df_train.iterrows():
            if pd.notna(row.get('tags', '')):
                tags = [tag.strip() for tag in str(row['tags']).split(',')]
                training_tags.extend(tags)
        
        training_counts = Counter(training_tags)
        
        # Analyze prediction tag patterns
        prediction_tags = []
        for _, row in self.df_results.iterrows():
            tags = row['tags'].split(',')
            prediction_tags.extend(tags)
        
        prediction_counts = Counter(prediction_tags)
        
        print(f"Training data tag frequency vs Prediction frequency:")
        print(f"{'Tag':<25} {'Training %':<12} {'Prediction %':<15} {'Ratio':<8}")
        print("-" * 65)
        
        for tag in list(training_counts.most_common(10)):
            tag_name = tag[0]
            train_pct = (training_counts[tag_name] / len(self.df_train)) * 100
            pred_pct = (prediction_counts[tag_name] / len(self.df_results)) * 100
            ratio = pred_pct / train_pct if train_pct > 0 else 0
            
            print(f"{tag_name:<25} {train_pct:<12.1f} {pred_pct:<15.1f} {ratio:<8.2f}")
    
    def run_complete_analysis(self):
        """Run complete manual confidence analysis"""
        print("=== MANUAL CONFIDENCE ANALYSIS ===")
        
        self.load_data()
        self.analyze_sample_predictions(10)
        self.analyze_585_1000_usage()
        self.analyze_tag_distribution()
        self.compare_with_training_patterns()
        
        print(f"\n=== ANALYSIS COMPLETE ===")

if __name__ == "__main__":
    analyzer = ManualConfidenceAnalyzer()
    analyzer.run_complete_analysis()
