#!/usr/bin/env python3

import pandas as pd
import ast
import re

def explore_data():
    print("=== EXPLORING DATA STRUCTURE ===")
    
    # Load the master tag vocabulary
    print("\n1. Loading master tag vocabulary...")
    with open('all_tags.txt', 'r') as f:
        tags_content = f.read().strip()
    
    # Parse the Python list
    all_tags = ast.literal_eval(tags_content)
    print(f"Total tags in vocabulary: {len(all_tags)}")
    print(f"First 10 tags: {all_tags[:10]}")
    
    # Load training data
    print("\n2. Loading training data...")
    try:
        df_train = pd.read_csv('products_with_tags.csv')
        print(f"Training data shape: {df_train.shape}")
        print(f"Columns: {list(df_train.columns)}")
        
        # Find the key columns we need
        key_columns = []
        for col in df_train.columns:
            if 'id' in col.lower() and 'gid://shopify/Product/' in str(df_train[col].iloc[0] if len(df_train) > 0 else ''):
                key_columns.append(('id', col))
            elif 'title' in col.lower() and col != 'title':
                key_columns.append(('title', col))
            elif 'description' in col.lower():
                key_columns.append(('description', col))
            elif 'tags' in col.lower():
                key_columns.append(('tags', col))
        
        print(f"\nKey columns found: {key_columns}")
        
        # Check the main columns
        if 'id' in df_train.columns:
            print(f"\nSample IDs: {df_train['id'].head(3).tolist()}")
        if 'title' in df_train.columns:
            print(f"Sample titles: {df_train['title'].head(3).tolist()}")
        if 'tags' in df_train.columns:
            print(f"Sample tags: {df_train['tags'].head(3).tolist()}")
            
        # Check product_detail columns
        product_detail_cols = [col for col in df_train.columns if col.startswith('product_detail.')]
        print(f"\nProduct detail columns ({len(product_detail_cols)}): {product_detail_cols[:10]}...")
        
    except Exception as e:
        print(f"Error loading training data: {e}")
    
    # Load target data
    print("\n3. Loading target data...")
    try:
        df_target = pd.read_csv('products_without_tags.csv')
        print(f"Target data shape: {df_target.shape}")
        
        # Check if it has the same structure
        print(f"Same columns as training: {list(df_train.columns) == list(df_target.columns)}")
        
    except Exception as e:
        print(f"Error loading target data: {e}")

if __name__ == "__main__":
    explore_data()
