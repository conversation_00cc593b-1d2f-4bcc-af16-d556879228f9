#!/usr/bin/env python3

import pandas as pd
import numpy as np
import ast
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import MultiLabelBinarizer
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

class ImprovedProductTaggingSystem:
    def __init__(self):
        self.all_tags = []
        self.tag_set = set()
        self.df_train = None
        self.df_target = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.tag_patterns = defaultdict(list)
        
        # Material conflict detection
        self.material_groups = {
            'gold': ['gold', 'yellow gold', 'white gold', 'rose gold', '14kt', '14k', '585/1000'],
            'silver': ['silver', 'sterling silver', '925/1000', 'stříbro'],
            'platinum': ['platinum', 'platina']
        }
        
        # Czech language tags
        self.czech_tags = {
            'prsten', 'prsteny', 'n<PERSON><PERSON>ek', 'n<PERSON><PERSON><PERSON><PERSON><PERSON>', 'p<PERSON>ív<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            'b<PERSON><PERSON> zlato', 'žlut<PERSON> zlato', 'rů<PERSON><PERSON><PERSON> zlato', 'stříbro',
            'snubní prsteny', 'zásnubní prsten', 'bile zlato prsteny',
            'prsteny bílé zlato', 'snubni prsteny zlato', 'Žluté zlato'
        }
        
        # Over-assigned tags to control
        self.controlled_tags = {
            'Cubic Zirconia': 0.3,  # Reduce from 92.7% to ~30%
            'transparent': 0.2,     # Reduce from 48.6% to ~20%
            'yellow': 0.25,         # Reduce from 51.2% to ~25%
            'tinge': 0.1           # Very selective usage
        }
        
    def load_data(self):
        """Load all required data files"""
        print("Loading data...")
        
        # Load master tag vocabulary
        with open('all_tags.txt', 'r') as f:
            tags_content = f.read().strip()
        self.all_tags = ast.literal_eval(tags_content)
        self.tag_set = set(self.all_tags)
        print(f"Loaded {len(self.all_tags)} tags from vocabulary")
        
        # Load training data
        self.df_train = pd.read_csv('products_with_tags.csv', low_memory=False)
        print(f"Loaded training data: {self.df_train.shape}")
        
        # Load target data
        self.df_target = pd.read_csv('products_without_tags.csv', low_memory=False)
        print(f"Loaded target data: {self.df_target.shape}")
        
    def clean_and_prepare_data(self):
        """Clean and prepare the data for processing"""
        print("Cleaning and preparing data...")
        
        # Clean training data tags
        self.df_train['clean_tags'] = self.df_train['tags'].apply(self._clean_tags)
        
        # Create combined text features for similarity matching
        self.df_train['combined_text'] = self._create_combined_text(self.df_train)
        self.df_target['combined_text'] = self._create_combined_text(self.df_target)
        
        # Remove rows with no valid tags
        self.df_train = self.df_train[self.df_train['clean_tags'].apply(len) > 0]
        print(f"Training data after cleaning: {self.df_train.shape}")
        
    def _clean_tags(self, tag_string):
        """Clean and validate tags against the master vocabulary"""
        if pd.isna(tag_string) or tag_string == '':
            return []
        
        # Split by comma and clean
        tags = [tag.strip() for tag in str(tag_string).split(',')]
        
        # Filter to only include tags from the master vocabulary
        valid_tags = [tag for tag in tags if tag in self.tag_set]
        
        return valid_tags
    
    def _create_combined_text(self, df):
        """Create combined text from title, description, and product details"""
        combined_texts = []
        
        for _, row in df.iterrows():
            text_parts = []
            
            # Add title
            if pd.notna(row.get('title', '')):
                text_parts.append(str(row['title']))
            
            # Add description
            if pd.notna(row.get('description', '')):
                text_parts.append(str(row['description']))
            
            # Add key product details
            detail_fields = [
                'product_detail.Color', 'product_detail.Material ', 
                'product_detail.Stone', 'product_detail.Jewelry type ',
                'product_detail.Setting ', 'product_detail.Cut ',
                'product_detail.Style ', 'product_detail.Gender '
            ]
            
            for field in detail_fields:
                if field in df.columns and pd.notna(row.get(field, '')):
                    text_parts.append(str(row[field]))
            
            combined_texts.append(' '.join(text_parts))
        
        return combined_texts
    
    def build_similarity_model(self):
        """Build TF-IDF model for similarity matching"""
        print("Building similarity model...")
        
        # Create TF-IDF vectorizer with higher thresholds
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=3000,  # Reduced for better precision
            stop_words='english',
            ngram_range=(1, 2),
            min_df=3,  # Increased minimum document frequency
            max_df=0.8  # Added maximum document frequency
        )
        
        # Fit on training data
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(self.df_train['combined_text'])
        print(f"TF-IDF matrix shape: {self.tfidf_matrix.shape}")
    
    def analyze_tag_patterns(self):
        """Analyze tag co-occurrence patterns"""
        print("Analyzing tag patterns...")
        
        # Build tag co-occurrence patterns
        for _, row in self.df_train.iterrows():
            tags = row['clean_tags']
            product_type = row.get('productType', 'Unknown')
            
            # Store tags by product type
            self.tag_patterns[product_type].extend(tags)
        
        # Calculate tag frequencies by product type
        for product_type in self.tag_patterns:
            tag_counts = Counter(self.tag_patterns[product_type])
            self.tag_patterns[product_type] = tag_counts
    
    def _detect_material_conflicts(self, target_idx, candidate_tags):
        """Detect and resolve material conflicts"""
        target_row = self.df_target.iloc[target_idx]
        
        # Get product material information
        title = str(target_row.get('title', '')).lower()
        description = str(target_row.get('description', '')).lower()
        material = str(target_row.get('product_detail.Material ', '')).lower()
        
        combined_text = f"{title} {description} {material}"
        
        # Detect primary material
        primary_material = None
        for material_type, keywords in self.material_groups.items():
            if any(keyword.lower() in combined_text for keyword in keywords):
                primary_material = material_type
                break
        
        if not primary_material:
            return candidate_tags  # No clear material detected
        
        # Filter out conflicting material tags
        filtered_tags = []
        for tag in candidate_tags:
            tag_lower = tag.lower()
            
            # Check if tag conflicts with detected material
            is_conflicting = False
            for other_material, keywords in self.material_groups.items():
                if other_material != primary_material:
                    if any(keyword.lower() in tag_lower for keyword in keywords):
                        is_conflicting = True
                        break
            
            if not is_conflicting:
                filtered_tags.append(tag)
        
        return filtered_tags
    
    def _should_include_czech_tag(self, target_idx, tag):
        """Determine if Czech tag should be included"""
        target_row = self.df_target.iloc[target_idx]
        
        # Check for Czech context indicators
        title = str(target_row.get('title', '')).lower()
        description = str(target_row.get('description', '')).lower()
        
        combined_text = f"{title} {description}"
        
        # Czech indicators
        czech_indicators = ['czech', 'praha', 'brno', 'česk', 'cz']
        
        # If explicit Czech context, allow Czech tags
        if any(indicator in combined_text for indicator in czech_indicators):
            return True
        
        # Otherwise, limit Czech tags significantly
        return False
    
    def _apply_controlled_tag_limits(self, target_idx, candidate_tags):
        """Apply limits to over-assigned tags"""
        target_row = self.df_target.iloc[target_idx]
        title = str(target_row.get('title', '')).lower()
        description = str(target_row.get('description', '')).lower()
        
        combined_text = f"{title} {description}"
        
        filtered_tags = []
        
        for tag in candidate_tags:
            # Check if tag is in controlled list
            if tag in self.controlled_tags:
                # Apply stricter validation
                if tag == 'Cubic Zirconia':
                    # Only include if explicitly mentioned
                    if 'cubic zirconia' in combined_text or 'cz' in combined_text:
                        filtered_tags.append(tag)
                elif tag == 'transparent':
                    # Only include if transparency is explicitly mentioned
                    if any(word in combined_text for word in ['transparent', 'clear', 'crystal']):
                        filtered_tags.append(tag)
                elif tag == 'yellow':
                    # Only include if yellow is explicitly mentioned or gold context
                    if 'yellow' in combined_text or ('gold' in combined_text and 'yellow gold' in combined_text):
                        filtered_tags.append(tag)
                # Skip 'tinge' unless very specific context
                elif tag == 'tinge' and 'tinge' in combined_text:
                    filtered_tags.append(tag)
            else:
                filtered_tags.append(tag)
        
        return filtered_tags
    
    def predict_tags_for_product(self, target_idx, n_similar=8):
        """Predict tags for a single product with improved filtering"""
        target_text = self.df_target.iloc[target_idx]['combined_text']
        target_vector = self.tfidf_vectorizer.transform([target_text])
        
        # Find most similar products with higher threshold
        similarities = cosine_similarity(target_vector, self.tfidf_matrix).flatten()
        similar_indices = similarities.argsort()[-n_similar:][::-1]
        
        # Collect tags from similar products with confidence scoring
        tag_scores = defaultdict(float)
        
        for idx in similar_indices:
            similarity_score = similarities[idx]
            if similarity_score > 0.15:  # Increased threshold
                similar_tags = self.df_train.iloc[idx]['clean_tags']
                for tag in similar_tags:
                    tag_scores[tag] += similarity_score
        
        # Add product type based tags (reduced weight)
        target_product_type = self.df_target.iloc[target_idx].get('productType', 'Unknown')
        if target_product_type in self.tag_patterns:
            type_tags = self.tag_patterns[target_product_type]
            for tag, count in type_tags.most_common(15):  # Reduced from 20
                tag_scores[tag] += count * 0.05  # Reduced weight
        
        # Sort tags by score
        sorted_tags = sorted(tag_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Extract candidate tags
        candidate_tags = []
        for tag, score in sorted_tags:
            if tag == '585/1000':
                # Keep existing strict validation
                if self._check_585_1000_confidence(target_idx, score):
                    candidate_tags.append(tag)
            else:
                candidate_tags.append(tag)
        
        # Apply improvement filters
        candidate_tags = self._detect_material_conflicts(target_idx, candidate_tags)
        candidate_tags = self._apply_controlled_tag_limits(target_idx, candidate_tags)
        
        # Filter Czech tags
        filtered_tags = []
        for tag in candidate_tags:
            if tag in self.czech_tags:
                if self._should_include_czech_tag(target_idx, tag):
                    filtered_tags.append(tag)
            else:
                filtered_tags.append(tag)
        
        # Vary tag count between 12-18 for better distribution
        if len(filtered_tags) >= 16:
            tag_count = min(18, len(filtered_tags))
        elif len(filtered_tags) >= 12:
            tag_count = min(15, len(filtered_tags))
        else:
            tag_count = max(12, len(filtered_tags))
        
        return filtered_tags[:tag_count]
    
    def _check_585_1000_confidence(self, target_idx, score):
        """Special validation for 585/1000 tag"""
        target_text = self.df_target.iloc[target_idx]['combined_text'].lower()
        
        # Check for explicit mentions
        explicit_mentions = ['585', '14k', '14kt', 'gold']
        if any(mention in target_text for mention in explicit_mentions):
            return score > 0.3  # Slightly reduced threshold
        
        return False
