#!/usr/bin/env python3

import pandas as pd
import ast
from collections import Counter

def validate_results():
    print("=== VALIDATING TAGGING RESULTS ===")

    # Load master tag vocabulary
    with open('all_tags.txt', 'r') as f:
        tags_content = f.read().strip()
    all_tags = set(ast.literal_eval(tags_content))
    print(f"Master vocabulary contains {len(all_tags)} tags")

    # Load results
    df_results = pd.read_csv('products_with_predicted_tags.csv')
    print(f"Results contain {len(df_results)} products")
    
    # Validation checks
    validation_errors = []
    tag_counts = []
    invalid_tags = set()
    products_with_585_1000 = 0
    
    for idx, row in df_results.iterrows():
        product_id = row['id']
        tags_str = row['tags']
        
        # Parse tags
        tags = [tag.strip() for tag in tags_str.split(',')]
        tag_counts.append(len(tags))
        
        # Check for invalid tags
        for tag in tags:
            if tag not in all_tags:
                invalid_tags.add(tag)
                validation_errors.append(f"Product {product_id}: Invalid tag '{tag}'")
        
        # Check for 585/1000 usage
        if '585/1000' in tags:
            products_with_585_1000 += 1
        
        # Check for duplicates
        if len(tags) != len(set(tags)):
            validation_errors.append(f"Product {product_id}: Duplicate tags found")
        
        # Check minimum tags
        if len(tags) < 5:
            validation_errors.append(f"Product {product_id}: Only {len(tags)} tags (minimum 5 recommended)")
    
    # Print validation summary
    print(f"\n=== VALIDATION SUMMARY ===")
    print(f"Total products processed: {len(df_results)}")
    print(f"Validation errors: {len(validation_errors)}")
    
    if invalid_tags:
        print(f"Invalid tags found: {len(invalid_tags)}")
        print(f"Invalid tags: {list(invalid_tags)[:10]}...")  # Show first 10
    else:
        print("✓ All tags are valid (present in master vocabulary)")
    
    # Tag count statistics
    tag_count_stats = {
        'min': min(tag_counts),
        'max': max(tag_counts),
        'avg': sum(tag_counts) / len(tag_counts),
        'median': sorted(tag_counts)[len(tag_counts)//2]
    }
    
    print(f"\n=== TAG COUNT STATISTICS ===")
    print(f"Min tags per product: {tag_count_stats['min']}")
    print(f"Max tags per product: {tag_count_stats['max']}")
    print(f"Average tags per product: {tag_count_stats['avg']:.1f}")
    print(f"Median tags per product: {tag_count_stats['median']}")
    
    # Count products by tag count ranges
    count_ranges = {
        '1-5': len([c for c in tag_counts if 1 <= c <= 5]),
        '6-10': len([c for c in tag_counts if 6 <= c <= 10]),
        '11-15': len([c for c in tag_counts if 11 <= c <= 15]),
        '16-20': len([c for c in tag_counts if 16 <= c <= 20]),
        '21+': len([c for c in tag_counts if c > 20])
    }
    
    print(f"\n=== TAG COUNT DISTRIBUTION ===")
    for range_name, count in count_ranges.items():
        percentage = (count / len(tag_counts)) * 100
        print(f"{range_name} tags: {count} products ({percentage:.1f}%)")
    
    print(f"\n=== SPECIAL TAG USAGE ===")
    print(f"Products with '585/1000' tag: {products_with_585_1000}")
    
    # Sample some results
    print(f"\n=== SAMPLE RESULTS ===")
    for i in range(min(3, len(df_results))):
        product_id = df_results.iloc[i]['id']
        tags = df_results.iloc[i]['tags']
        tag_list = tags.split(',')
        print(f"Product {product_id}:")
        print(f"  Tags ({len(tag_list)}): {', '.join(tag_list[:10])}{'...' if len(tag_list) > 10 else ''}")
    
    # Show validation errors (first 10)
    if validation_errors:
        print(f"\n=== VALIDATION ERRORS (First 10) ===")
        for error in validation_errors[:10]:
            print(f"  {error}")
    
    print(f"\n=== VALIDATION COMPLETE ===")
    return len(validation_errors) == 0

if __name__ == "__main__":
    is_valid = validate_results()
    if is_valid:
        print("✓ All validations passed!")
    else:
        print("⚠ Some validation issues found - see details above")
