#!/usr/bin/env python3

import pandas as pd
import numpy as np
import ast
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import MultiLabelBinarizer
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

class ProductTaggingSystem:
    def __init__(self):
        self.all_tags = []
        self.tag_set = set()
        self.df_train = None
        self.df_target = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.tag_patterns = defaultdict(list)
        
    def load_data(self):
        """Load all required data files"""
        print("Loading data...")
        
        # Load master tag vocabulary
        with open('all_tags.txt', 'r') as f:
            tags_content = f.read().strip()
        self.all_tags = ast.literal_eval(tags_content)
        self.tag_set = set(self.all_tags)
        print(f"Loaded {len(self.all_tags)} tags from vocabulary")
        
        # Load training data
        self.df_train = pd.read_csv('products_with_tags.csv', low_memory=False)
        print(f"Loaded training data: {self.df_train.shape}")
        
        # Load target data
        self.df_target = pd.read_csv('products_without_tags.csv', low_memory=False)
        print(f"Loaded target data: {self.df_target.shape}")
        
    def clean_and_prepare_data(self):
        """Clean and prepare the data for processing"""
        print("Cleaning and preparing data...")
        
        # Clean training data tags
        self.df_train['clean_tags'] = self.df_train['tags'].apply(self._clean_tags)
        
        # Create combined text features for similarity matching
        self.df_train['combined_text'] = self._create_combined_text(self.df_train)
        self.df_target['combined_text'] = self._create_combined_text(self.df_target)
        
        # Remove rows with no valid tags
        self.df_train = self.df_train[self.df_train['clean_tags'].apply(len) > 0]
        print(f"Training data after cleaning: {self.df_train.shape}")
        
    def _clean_tags(self, tag_string):
        """Clean and validate tags against the master vocabulary"""
        if pd.isna(tag_string) or tag_string == '':
            return []
        
        # Split by comma and clean
        tags = [tag.strip() for tag in str(tag_string).split(',')]
        
        # Filter to only include tags from the master vocabulary
        valid_tags = [tag for tag in tags if tag in self.tag_set]
        
        return valid_tags
    
    def _create_combined_text(self, df):
        """Create combined text from title, description, and product details"""
        combined_texts = []
        
        for _, row in df.iterrows():
            text_parts = []
            
            # Add title
            if pd.notna(row.get('title', '')):
                text_parts.append(str(row['title']))
            
            # Add description
            if pd.notna(row.get('description', '')):
                text_parts.append(str(row['description']))
            
            # Add key product details
            detail_fields = [
                'product_detail.Color', 'product_detail.Material ', 
                'product_detail.Stone', 'product_detail.Jewelry type ',
                'product_detail.Setting ', 'product_detail.Cut ',
                'product_detail.Style ', 'product_detail.Gender '
            ]
            
            for field in detail_fields:
                if field in df.columns and pd.notna(row.get(field, '')):
                    text_parts.append(str(row[field]))
            
            combined_texts.append(' '.join(text_parts))
        
        return combined_texts
    
    def build_similarity_model(self):
        """Build TF-IDF model for similarity matching"""
        print("Building similarity model...")
        
        # Create TF-IDF vectorizer
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2
        )
        
        # Fit on training data
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(self.df_train['combined_text'])
        print(f"TF-IDF matrix shape: {self.tfidf_matrix.shape}")
    
    def analyze_tag_patterns(self):
        """Analyze tag co-occurrence patterns"""
        print("Analyzing tag patterns...")
        
        # Build tag co-occurrence patterns
        for _, row in self.df_train.iterrows():
            tags = row['clean_tags']
            product_type = row.get('productType', 'Unknown')
            
            # Store tags by product type
            self.tag_patterns[product_type].extend(tags)
        
        # Calculate tag frequencies by product type
        for product_type in self.tag_patterns:
            tag_counts = Counter(self.tag_patterns[product_type])
            self.tag_patterns[product_type] = tag_counts
    
    def predict_tags_for_product(self, target_idx, n_similar=10):
        """Predict tags for a single product"""
        target_text = self.df_target.iloc[target_idx]['combined_text']
        target_vector = self.tfidf_vectorizer.transform([target_text])
        
        # Find most similar products
        similarities = cosine_similarity(target_vector, self.tfidf_matrix).flatten()
        similar_indices = similarities.argsort()[-n_similar:][::-1]
        
        # Collect tags from similar products
        tag_scores = defaultdict(float)
        
        for idx in similar_indices:
            similarity_score = similarities[idx]
            if similarity_score > 0.1:  # Minimum similarity threshold
                similar_tags = self.df_train.iloc[idx]['clean_tags']
                for tag in similar_tags:
                    tag_scores[tag] += similarity_score
        
        # Add product type based tags
        target_product_type = self.df_target.iloc[target_idx].get('productType', 'Unknown')
        if target_product_type in self.tag_patterns:
            type_tags = self.tag_patterns[target_product_type]
            for tag, count in type_tags.most_common(20):
                tag_scores[tag] += count * 0.1  # Weight type-based tags lower
        
        # Sort tags by score and select top ones
        sorted_tags = sorted(tag_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Apply special rules for 585/1000 tag
        predicted_tags = []
        for tag, score in sorted_tags:
            if tag == '585/1000':
                # Only include if explicitly mentioned or very high confidence
                if self._check_585_1000_confidence(target_idx, score):
                    predicted_tags.append(tag)
            else:
                predicted_tags.append(tag)
        
        # Limit to 10-20 tags
        return predicted_tags[:20]
    
    def _check_585_1000_confidence(self, target_idx, score):
        """Special validation for 585/1000 tag"""
        target_text = self.df_target.iloc[target_idx]['combined_text'].lower()
        
        # Check for explicit mentions
        explicit_mentions = ['585', '14k', '14kt', 'gold']
        if any(mention in target_text for mention in explicit_mentions):
            return score > 0.5
        
        return False
    
    def predict_all_tags(self):
        """Predict tags for all target products"""
        print("Predicting tags for all products...")
        
        results = []
        
        for idx in range(len(self.df_target)):
            if idx % 100 == 0:
                print(f"Processing product {idx+1}/{len(self.df_target)}")
            
            product_id = self.df_target.iloc[idx]['id']
            predicted_tags = self.predict_tags_for_product(idx)
            
            # Ensure minimum number of tags
            if len(predicted_tags) < 5:
                # Add generic tags based on product type
                product_type = self.df_target.iloc[idx].get('productType', '')
                if 'ring' in product_type.lower():
                    predicted_tags.extend(['ring', 'jewelry', 'gold'])
                elif 'earring' in product_type.lower():
                    predicted_tags.extend(['earrings', 'jewelry', 'gold'])
                elif 'necklace' in product_type.lower() or 'pendant' in product_type.lower():
                    predicted_tags.extend(['necklace', 'pendant', 'jewelry', 'gold'])
                elif 'bracelet' in product_type.lower():
                    predicted_tags.extend(['bracelet', 'jewelry', 'gold'])
            
            # Remove duplicates and limit
            predicted_tags = list(dict.fromkeys(predicted_tags))[:20]
            
            results.append({
                'id': product_id,
                'tags': ','.join(predicted_tags)
            })
        
        return results
    
    def save_results(self, results):
        """Save results to CSV file"""
        print("Saving results...")
        
        df_results = pd.DataFrame(results)
        df_results.to_csv('products_with_predicted_tags.csv', index=False)
        print(f"Saved {len(results)} predictions to products_with_predicted_tags.csv")
    
    def run_complete_pipeline(self):
        """Run the complete tagging pipeline"""
        print("=== STARTING ML-BASED PRODUCT TAGGING SYSTEM ===")
        
        self.load_data()
        self.clean_and_prepare_data()
        self.build_similarity_model()
        self.analyze_tag_patterns()
        
        results = self.predict_all_tags()
        self.save_results(results)
        
        print("=== TAGGING COMPLETE ===")
        return results

if __name__ == "__main__":
    system = ProductTaggingSystem()
    results = system.run_complete_pipeline()
